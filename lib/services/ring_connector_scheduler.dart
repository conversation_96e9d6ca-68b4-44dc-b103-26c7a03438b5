import 'dart:async';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:SAiWELL/services/analytics/events.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../common_controllers/global_controller.dart';
import 'native_communicator.dart';

class RingConnectorScheduler {
  RingConnectorScheduler._privateConstructor();

  static final RingConnectorScheduler _instance =
      RingConnectorScheduler._privateConstructor();

  factory RingConnectorScheduler() {
    return _instance;
  }

  Timer? t;
  PrefsService prefsService = PrefsService();
  NativeCommunicator communicator = NativeCommunicator();
  GlobalController globalController = Get.find<GlobalController>();
  bool isFirstTimeWebViewLoad = true;
  bool isEventFired = true;
  int retryCount = 0;
  //RingDataCollector ringDataCollector = RingDataCollector();

  void startRingCollection({
    required Future<void> Function() checkAndFetchData,
    required void Function() reloadWebView,
    required void Function() updateBatteryStatusToWebview,
    required Null Function() updateDeviceConnectionDetailsToWebview,
  }) {
    if (t != null || t?.isActive == true) {
      t?.cancel();
    }

    if (globalController.shouldShowNavbar.value) {
      autoConnectAction(updateBatteryStatusToWebview, reloadWebView,
          updateBatteryStatusToWebview);
    }

    // Set up periodic timer with reduced frequency to improve performance
    t = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (globalController.shouldShowNavbar.value) {
        autoConnectAction(updateBatteryStatusToWebview, reloadWebView,
            updateDeviceConnectionDetailsToWebview);
      }
    });

    Future.delayed(const Duration(seconds: 10), () async {
      if (globalController.shouldShowNavbar.value) {
        bool isConnected = await communicator.getConnectionStateV2();
        if (isConnected) {
          await checkAndFetchData();
          if (isConnected && !isEventFired) {
            LogEvents.logRingAutoConnectEvent(
                batteryPercent: globalController.ringBatteryPercentage.value);
            isEventFired = true;
          }
        }
        Timer.periodic(const Duration(seconds: 60), (timer) async {
          bool isConnected = await communicator.getConnectionStateV2();
          if (isConnected) {
            await checkAndFetchData();
            if (isConnected && !isEventFired) {
              LogEvents.logRingAutoConnectEvent(
                  batteryPercent: globalController.ringBatteryPercentage.value);
              isEventFired = true;
            }
          }
        });
      }
    });
  }

  void autoConnectAction(
    void Function() updateBetteryStatusInWebView,
    void Function() reloadWebView,
    void Function() updateDeviceConnectionDetailsToWebview,
  ) async {
    String macId = await prefsService.getLastConnectedDeviceMac();
    String ringName = prefsService.getLastConnectedDeviceName();
    bool isConnected = await communicator.getConnectionStateV2();
    print("[connectAction] isConnected -----$isConnected");
    String uid = await prefsService.getUid();
    int batteryValue = globalController.ringBatteryPercentage.value;

    if (isConnected && uid != "" && batteryValue == 0) {
      globalController.isConnected.value = true;
      int battery = await communicator.getBatteryLevelV2();
      globalController.ringBatteryPercentage.value = battery;
      updateBetteryStatusInWebView();
      updateDeviceConnectionDetailsToWebview();
    }

    if (macId != "" && !isConnected) {
      if (retryCount < 3) {
        retryCount++;
        globalController.currentToastStatus.value =
            CurrentToastStatus.ringConnection;
      }

      print("___NAVIGATING-----------before -");
      await communicator.connectToDeviceV2(
        macId: macId,
        updateRingStatusToWebView: null,
        reloadWebView: reloadWebView,
        updateDeviceConnectionDetailsToWebview:
            updateDeviceConnectionDetailsToWebview,
        updateBetteryStatusInWebView: updateBetteryStatusInWebView,
      );
      globalController.ringMacAddress.value = macId;
      globalController.ringName.value = ringName;
      bool isConnected = false;
      int maxTry = 4;

      do {
        isConnected = await communicator.getConnectionStateV2();
        debugPrint(
            "outside isConnected != globalController.isConnected.value isConnected:$isConnected");
        if (isConnected != globalController.isConnected.value) {
          debugPrint(
              "inside isConnected != globalController.isConnected.value");
          globalController.isConnected.value = isConnected;
          if (isConnected) {
            globalController.currentToastStatus.value =
                CurrentToastStatus.dataSyncing;
          }
        }
        await Future.delayed(const Duration(milliseconds: 500));
      } while (!isConnected && --maxTry > 0);
      if (maxTry <= 0 && !isConnected && retryCount == 3) {
        globalController.currentToastStatus.value = CurrentToastStatus.start;
      }
      print("___NAVIGATING------------");
    }
  }
}
